export function ApiGetCert(code: string): Promise<CertData> {
  return useRequest({
    url: `/webApi/dzzs/verify/${code}`,
    method: 'get',
    timeout: 60000, // 接口慢，设置超时时间为 1 分钟
  })
}

const mockData = {
  title: '小学毕业证',
  type: '1',
  xm: '马六',
  xb: '男',
  csrq: '2009-01-01',
  jg: '潍坊潍城区',
  zcxh: '3707261123401',
  fzbh: 'BYZS19900101001',
  zsxl: '小学毕业',
  bysj: '2025-07',
  xlUrl: null,
  qfsj: '2025-06-05',
  qfdw: '潍坊高新技术产业开发区清平小学',
  txFile: '/profile/20250605/verify/ce3c5f5be692848ad37426f084d051a8.png',
  pdfFile: '/profile/20250605/verify/ce3c5f5be692848ad37426f084d051a8.pdf',
  ofdFile: '/profile/20250605/verify/ce3c5f5be692848ad37426f084d051a8.ofd',
  imageFile: '/profile/20250605/verify/ce3c5f5be692848ad37426f084d051a8.png',
  sealInfoList: [
    { sealName: '潍坊高新技术产业开发区清平小学魏涛', sealPic: null, sealTime: '2025-06-05 14:36:35' },
    { sealName: '潍坊高新技术产业开发区清平小学公章', sealPic: '/profile/upload/qpxxgz.png', sealTime: '2025-06-05 14:38:01' },
    { sealName: '潍坊高新技术产业开发区教育和体育局公章', sealPic: '/profile/upload/qpxxgz.png', sealTime: '2025-06-05 14:39:30' },
  ],
}

export function ApiGetCertMock(code: string): Promise<CertData> {
  return Promise.resolve(mockData as any)
}
