import { saveAs } from 'file-saver'
import { showDialog, showImagePreview } from 'vant'

// 获取文件后缀名
function getFileExtension(url: string): string {
  const match = url.match(/\.([^.]+)$/)
  return match ? match[1].toLowerCase() : ''
}

// 原生下载方法
function nativeDownload(url: string, fileName: string): void {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 下载文件并重命名
async function downloadFile(url: string, fileName?: string): Promise<void> {
  const extension = getFileExtension(url)
  const finalFileName = fileName ? `${fileName}.${extension}` : `电子证书_${new Date().getTime()}.${extension}`

  try {
    // 优先使用 file-saver 下载
    const response = await fetch(url)
    const blob = await response.blob()
    saveAs(blob, finalFileName)
  }
  catch {
    // 降级原生下载方案
    nativeDownload(url, finalFileName)
  }
}

export function handleFileAction(url: string | null, type: string, fileName?: string): void {
  if (!url) {
    showDialog({
      title: '电子证照签发中',
      message: '您的证书已由学校制作完成，待教育主管部门签发后，即可查看电子证照。',
      theme: 'round-button',
      confirmButtonText: '已知晓',
    })
    return
  }

  // 图片操作
  if (type === 'image') {
    showImagePreview({
      images: [url],
      closeable: true,
    })
    return
  }

  // 文件操作
  if (type === 'file') {
    downloadFile(url, fileName)
  }
}
