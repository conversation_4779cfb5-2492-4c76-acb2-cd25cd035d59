import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Index',
    component: () => import('~/views/index.vue'),
    meta: { title: import.meta.env.VITE_TITLE, layout: 'default', showBack: false },
  },
  {
    path: '/:code',
    name: 'Code',
    component: () => import('~/views/code.vue'),
    meta: { title: '扫码中...', layout: 'empty' },
  },
  {
    path: '/verify',
    name: 'Verify',
    component: () => import('~/views/verify/index.vue'),
    meta: { title: '电子证照核验结果', layout: 'verify' },
  },
  {
    path: '/guide',
    name: 'Guide',
    component: () => import('~/views/guide/index.vue'),
    meta: { title: '电子证照操作指南', layout: 'default', showBack: false },
  },
  {
    path: '/guide/receive',
    name: 'GuideReceive',
    component: () => import('~/views/guide/receive.vue'),
    meta: { title: '电子证照申领说明', layout: 'default', showBack: '/guide', dynamicBack: true },
  },
  {
    path: '/guide/verify',
    name: 'GuideVerify',
    component: () => import('~/views/guide/verify.vue'),
    meta: { title: '电子证照验签说明', layout: 'default', showBack: '/guide', dynamicBack: true },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes,
})

router.beforeEach((to, from) => {
  // 实现动态返回路径
  if (to.meta.dynamicBack && from.name && to.fullPath !== from.fullPath)
    to.meta.dynamicBackPath = from.fullPath
})

export default router
