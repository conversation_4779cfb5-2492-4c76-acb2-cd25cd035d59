<script setup lang="ts">
const router = useRouter()
const route = useRoute()
const title = computed<string>(() => route.meta.title || import.meta.env.VITE_TITLE)
const showBack = computed(() => route.meta.showBack !== false)
function handleBack() {
  if (route.meta.dynamicBackPath) {
    router.push(route.meta.dynamicBackPath)
  }
  else if (typeof route.meta.showBack === 'string') {
    router.push(route.meta.showBack)
  }
  else {
    router.back()
  }
}
</script>

<template>
  <div class="mx-auto h-screen max-w-500px min-w-320px flex flex-col overflow-hidden bg-white text-gray-700 pb-safe">
    <!-- 头部区域 -->
    <header class="relative h-13 flex-shrink-0 from-blue-500 to-blue-600 bg-gradient-to-b py-3 text-white shadow-blue-900/20 shadow-lg">
      <!-- 返回按钮 -->
      <div v-if="showBack" class="absolute inset-y-0 left-0 flex items-center pl-3">
        <button class="h-8 w-8 flex items-center justify-center rounded-full hover:bg-white/20" @click="handleBack">
          <i class="i-mingcute:arrow-left-line text-xl" />
        </button>
      </div>
      <!-- 标题 -->
      <h1 class="text-center text-xl">
        {{ title }}
      </h1>
    </header>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto">
      <slot />
    </div>
  </div>
</template>
