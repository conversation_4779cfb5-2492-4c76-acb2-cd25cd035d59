<script setup lang="ts">
import defaultStamp from '~/assets/stamp_jtj.png'

const props = defineProps<{
  certData: CertData
}>()

// 个人信息字段定义
const personalInfoFields: FieldDefinition<CertData>[] = [
  { key: 'xb', label: '个人性别' },
  { key: 'csrq', label: '出生日期' },
  { key: 'jg', label: '籍贯信息' },
]

// 证书信息字段定义
const certificateInfoFields: FieldDefinition<CertData>[] = [
  { key: 'zsxl', label: '证书学历' },
  { key: 'bysj', label: '毕业时间' },
  { key: 'zcxh', label: '注册学号' },
  { key: 'fzbh', label: '发证编号' },
  { key: 'qfsj', label: '签发时间' },
  { key: 'qfdw', label: '签发单位' },
]

// 设置下载文件名
const downloadFileName = computed(() => {
  const { xm, title, fzbh } = props.certData
  return `${xm}_${title}_${fzbh}`
})

// 下载选项字段定义
const downloadOptions: FieldDefinition<CertData>[] = [
  // { key: 'pdfFile', label: '查看 PDF 证书', type: 'file', icon: 'i-mingcute:pdf-line' },
  { key: 'imageFile', label: '查看证书图片', type: 'image', icon: 'i-mingcute:photo-album-2-fill' },
  { key: 'ofdFile', label: '下载 OFD 证书', type: 'file', tip: '可验签', icon: 'i-mingcute:download-3-line' },
]

// 温馨提示数据
const tipsData = [
  {
    content: '当前展示内容为电子证照预览信息，您可下载 OFD 格式电子证照文件以查看完整信息。',
  },
  {
    content: '您可通过“爱山东”政务 APP 申领电子证照。',
    link: {
      text: '【操作说明】',
      to: '/guide/receive',
    },
  },
  {
    content: '您可对 OFD 格式电子证照文件执行验签操作，以验证信息完整且未经篡改。',
    link: {
      text: '【操作说明】',
      to: '/guide/verify',
    },
  },
]

// 从 sealInfoList 获取第一个有效的签章图片，否则使用默认图片
const stampImage: ComputedRef<string | null> = computed(() => {
  if (useEnv.VITE_SHOW_STAMP !== 'true') {
    return null
  }
  if (Array.isArray(props.certData?.sealInfoList)) {
    const seal = props.certData.sealInfoList.find((item: SealInfo) => item.sealPic)
    return seal?.sealPic ? seal.sealPic : defaultStamp
  }
  return defaultStamp
})

onMounted(() => {
  // 在组件挂载后创建水印
  const watermark = useWatermark()
  watermark.create()
})
</script>

<template>
  <div id="watermark" class="flex-1 overflow-y-auto bg-white pb-5 scrollbar-hide">
    <!-- 头部信息与照片区域 -->
    <div class="flex items-center bg-blue-50 p-4">
      <!-- 照片区域 -->
      <div class="mr-5 h-32 flex-shrink-0 overflow-hidden rounded bg-white p-1.5 shadow-xl">
        <img v-if="certData.txFile" :src="certData.txFile" alt="证件照" class="aspect-3/4 h-full object-cover">
        <div v-else class="aspect-3/4 h-full flex items-center justify-center bg-gray-200">
          <div class="i-mingcute:user-3-line text-5xl text-gray-400" />
        </div>
      </div>

      <!-- 姓名和基本信息 -->
      <div class="grid grid-cols-1 gap-2">
        <h2 class="text-xl font-bold">
          {{ certData.xm }}
        </h2>
        <div class="text-blue-700 font-semibold">
          证书学历：{{ certData.zsxl }}
        </div>
        <div class="text-blue-700 font-semibold">
          毕业时间：{{ certData.bysj }}
        </div>
      </div>
    </div>

    <!-- 核验状态标识 -->
    <div class="p-4">
      <div class="flex items-center justify-between border border-green-200 rounded-lg bg-green-50 p-3">
        <div class="flex items-center">
          <div class="mr-2 h-3 w-3 rounded-full bg-green-500" />
          <span class="text-green-700 font-medium">证照核验通过</span>
        </div>
        <div class="text-xs text-gray-500">
          发证编号：{{ certData.fzbh }}
        </div>
      </div>
    </div>

    <!-- 主要信息区域 -->
    <div class="flex flex-col gap-5 px-5 divide-y divide-gray-100">
      <!-- 个人信息区域 -->
      <div>
        <h3 class="mb-3 flex items-center text-blue-600 font-bold">
          <i class="i-mingcute:profile-line mr-1 text-xl" />
          <span class="text-lg">个人信息</span>
        </h3>
        <div class="flex flex-col gap-2">
          <div v-for="field in personalInfoFields" :key="field.key" class="flex items-start">
            <div class="mr-2 flex-shrink-0 text-gray-500">
              {{ field.label }}：
            </div>
            <div class="font-medium">
              {{ certData[field.key] }}
            </div>
          </div>
        </div>
      </div>

      <!-- 证书信息区域 -->
      <div class="py-5">
        <h3 class="mb-3 flex items-center justify-between text-blue-600 font-bold">
          <div class="flex items-center">
            <i class="i-mingcute:certificate-line mr-1 text-xl" />
            <span class="text-lg">证书信息</span>
          </div>
          <a
            v-if="!!certData.xlUrl" :href="certData.xlUrl" target="_blank"
            class="flex items-center text-xs text-orange-400 font-normal hover:text-orange-500"
          >
            <span>省学历证书查询</span>
            <i class="i-mingcute:external-link-line ml-1" />
          </a>
        </h3>
        <div class="flex flex-col gap-2">
          <div v-for="field in certificateInfoFields" :key="field.key" class="flex items-start">
            <div class="mr-2 flex-shrink-0 text-gray-500">
              {{ field.label }}：
            </div>
            <div class="font-medium">
              <template v-if="field.key === 'qfsj' && !certData[field.key]">
                <span class="text-rose-400">等待电子证照签发中...</span>
              </template>
              <template v-else>
                {{ certData[field.key] }}
              </template>
            </div>
          </div>
        </div>
        <img
          v-if="stampImage" :src="stampImage" alt="签章"
          class="absolute bottom-0 right-0 max-w-32 w-1/3 origin-bottom-right opacity-50"
        >
      </div>
    </div>

    <!-- 温馨提示区域（暂时关闭） -->
    <div class="mx-5 border-l-4 border-orange-300 rounded-lg bg-orange-50 px-4 py-3">
      <div class="flex items-center">
        <div class="flex flex-col gap-y-2">
          <p class="flex items-center text-sm text-orange-400">
            <i class="i-mingcute:information-line mr-1" />温馨提示：
          </p>
          <p v-for="(tip, index) in tipsData" :key="index" class="text-xs text-orange-400 leading-relaxed">
            {{ index + 1 }}. {{ tip.content }}
            <RouterLink v-if="tip.link" :to="tip.link.to" class="text-blue-500 hover:text-blue-600">
              {{ tip.link.text }}
            </RouterLink>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 下载按钮区域 -->
  <footer class="z-1001 flex-shrink-0 border-t border-gray-100 bg-gray-50 px-4 shadow-[0_-2px_8px_rgba(0,0,0,0.06)] pb-safe">
    <div class="grid grid-cols-2 mt-3 gap-4">
      <button
        v-for="option in downloadOptions" :key="option.key"
        class="flex select-none items-center justify-center gap-x-2px rounded-lg bg-blue-500 py-2 text-center text-white shadow-sm transition duration-200 hover:bg-blue-600"
        @click="option.type && handleFileAction(certData[option.key] as string | null, option.type, downloadFileName)"
      >
        <!-- <i :class="option.icon" /> -->
        <span class="text-sm">{{ option.label }}</span>
        <small v-if="option.tip" class="text-xs text-orange-400">[{{ option.tip }}]</small>
      </button>
    </div>

    <div class="relative mb-3 mt-2">
      <div class="absolute inset-0 flex items-center" aria-hidden="true">
        <div class="w-full border-t border-gray-300" />
      </div>
      <div class="relative flex justify-center">
        <span class="select-none bg-gray-50 px-4 text-xs text-gray-500">证书签发支持：潍坊高新区大数据中心</span>
      </div>
    </div>
  </footer>
</template>
