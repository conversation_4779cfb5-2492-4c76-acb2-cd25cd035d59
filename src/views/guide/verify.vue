<script setup lang="ts">
const steps: GuideStep[] = [
  {
    title: '获取电子证照文件',
    description: '下载 OFD 格式电子证照文件',
    images: [{ src: '/images/guide/verify/1.png' }],
  },
  {
    title: '电脑端验签操作流程',
    description: '使用 WPS 打开已下载的 OFD 格式电子证照文件，在目标电子印章区域执行右键操作。',
    images: [
      { src: '/images/guide/verify/2.png' },
      {
        src: '/images/guide/verify/3.png',
        caption: '执行签章验证，当系统返回【该签章有效】提示时，表明电子签章完整且未经篡改。',
      },
      {
        src: '/images/guide/verify/4.png',
        caption: ' 点击【查验详情】可查看签章信息及印章属性等数字凭证信息。',
      },
    ],
  },
  {
    title: '移动端验签操作流程',
    description: '在移动端安装 WPS 并打开 OFD 格式电子证照文件。系统将自动执行签章验证，若弹出提示【该文档的所有签章有效！内容未被修改】，则表明证照内容完整可信。',
    images: [
      { src: '/images/guide/verify/5.png' },
      {
        src: '/images/guide/verify/6.png',
        caption: '可通过手动点击【验章】功能进行主动签章验证操作。',
      },
      {
        src: '/images/guide/verify/7.png',
        caption: '验证通过后展示完整签章信息。',
      },
    ],
  },
]
</script>

<template>
  <GuideSteps title="OFD 格式电子证照验签步骤" :steps="steps" />
</template>
