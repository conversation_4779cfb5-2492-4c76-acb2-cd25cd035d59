<script setup lang="ts">
const steps: GuideStep[] = [
  {
    title: '访问个人中心',
    description: '启动“爱山东”政务 APP，进入【我的】功能模块。',
    images: [
      { src: '/images/guide/receive/1.png' },
    ],
  },
  {
    title: '进入电子卡包',
    description: '在【我的】界面定位【我的证照】功能区，点击右侧【查看全部】操作按钮。',
    images: [
      { src: '/images/guide/receive/2.png' },
    ],
  },
  {
    title: '完成身份核验',
    description: '根据系统指引执行人脸识别认证，点击【立即刷脸】进入电子卡包界面。',
    images: [
      { src: '/images/guide/receive/3.png' },
    ],
  },
  {
    title: '申领电子证照',
    description: '在【电子卡包】界面选择【证照申领】功能，于搜索栏输入"毕业证"执行检索，从结果列表中选择对应证书执行申领操作。',
    images: [
      { src: '/images/guide/receive/4.png' },
      { src: '/images/guide/receive/5.png' },
    ],
  },
  {
    title: '查看电子证照',
    description: '申领成功后返回【电子卡包】主界面，通过【我的证照】功能模块查看已申领的电子毕业证书。',
    images: [
      { src: '/images/guide/receive/6.png' },
    ],
  },
]
</script>

<template>
  <GuideSteps title="爱山东申领电子证照操作步骤" :steps="steps" />
</template>
