<script setup lang="ts">
defineProps<{
  title: string // 主标题
  steps: GuideStep[] // 操作步骤
}>()

const { pageRef, handleImageClick } = useImagePreview()
</script>

<template>
  <div ref="pageRef" class="p-4 pb-10">
    <h2 class="mb-6 text-center text-xl text-gray-800 font-bold">
      {{ title }}
    </h2>

    <div class="space-y-8">
      <div v-for="(step, index) in steps" :key="index">
        <h3 class="mb-2 flex items-center text-lg text-gray-700 font-semibold">
          <span
            class="mr-3 h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-blue-500 text-sm text-white font-bold"
          >
            {{ index + 1 }}
          </span>
          {{ step.title }}
        </h3>
        <p class="mb-4 text-gray-600">
          <i class="i-mingcute:down-fill mr-1 text-xs text-gray-400" />
          {{ step.description }}
        </p>
        <div class="space-y-4">
          <div v-for="img in step.images" :key="img.src">
            <p v-if="img.caption" class="mb-2 text-gray-600">
              <i class="i-mingcute:down-fill mr-1 text-xs text-gray-400" />
              {{ img.caption }}
            </p>
            <img
              :src="img.src"
              class="mx-auto block w-4/5 cursor-pointer rounded-lg shadow-md hover:opacity-90"
              @click="handleImageClick"
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
