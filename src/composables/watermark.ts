import type { WatermarkOptions } from 'watermark-js-plus'
import { Watermark } from 'watermark-js-plus'

/**
 * 响应式的水印创建和自动清理功能
 */
export function useWatermark(): {
  create: (options?: Partial<WatermarkOptions>) => void
  destroy: () => void
} {
  const watermarkInstance: Ref<Watermark | null> = ref(null) // 存储当前水印实例

  // 获取当前时间字符串
  const getCurrentTime = () => {
    return new Date().toLocaleString('zh-CN')
  }

  // 默认配置选项
  // 配置项文档：https://zhensherlock.github.io/watermark-js-plus/zh/config/
  const defaultOptions: Partial<WatermarkOptions> = {
    // 水印内容，可以是文本或图片 URL
    content: `${import.meta.env.VITE_TITLE}\n${getCurrentTime()}`,
    // 单个水印的宽度（像素）
    width: 200,
    // 单个水印的高度（像素）
    height: 280,
    // 水印旋转角度（度）
    rotate: 30,
    // 内容类型：'text' 文本 | 'image' 图片 | 'rich-text' 富文本 | 'multi-line-text' 多行文本
    contentType: 'multi-line-text',
    // 文本对齐方式：'top' | 'middle' | 'bottom' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left' | 'right'
    translatePlacement: 'middle',
    // 背景定位，格式：'x y, x y'，用于多水印定位
    backgroundPosition: '0 0, 0 0',
    // 背景重复方式：'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat'
    backgroundRepeat: 'repeat',
    // 水印容器的 CSS 选择器
    parent: '#watermark',
    // 水印层级
    zIndex: 1000,
    // 水印透明度（0-1）
    globalAlpha: 0.2,
    // 水印模式：'default' 默认 | 'blind' 盲水印
    mode: 'default',
    // 文本渲染方式：'fill' 填充 | 'stroke' 描边 | 'both' 两者
    textType: 'fill',
    // 文本行高（像素）
    lineHeight: 20,
    // 字体大小（支持 CSS 单位）
    fontSize: '16px',
    // 字体族：'serif' | 'sans-serif'
    fontFamily: 'serif',
    // 字体样式：'normal' | 'italic' | 'oblique'
    fontStyle: '',
    // 字体变体：'normal' | 'small-caps'
    fontVariant: '',
    // 字体颜色（支持 CSS 颜色值）
    fontColor: '#000',
    // 字体粗细：'normal' | 'bold' | 'bolder' | 'lighter' | 100-900
    fontWeight: 'normal',
    // CSS 滤镜效果
    filter: 'none',
    // 字符间距（支持 CSS 单位）
    letterSpacing: '0px',
    // 是否开启 MutationObserver 监听 DOM 变化
    mutationObserve: true,
    // 是否开启防篡改保护（防止控制台修改）
    monitorProtection: false,
    // 是否显示辅助线（调试用）
    auxiliaryLine: false,
    // 水印是否可滚动
    movable: false,
    // 布局方式：'default' 默认 | 'grid' 网格 | 'custom' 自定义
    layout: 'default',
    // 词间距（支持 CSS 单位）
    wordSpacing: 'normal',
    // 图片水印宽度（像素）
    imageWidth: 150,
    // 图片水印高度（像素）
    imageHeight: 150,
    // 高级样式配置
    advancedStyle: {
      // 渐变类型：'linear' 线性 | 'radial' 径向
      type: 'linear',
      // 渐变参数
      params: {},
      // 渐变色标点
      colorStops: [
        { offset: 0, color: 'green' },
        { offset: 1, color: 'blue' },
      ],
    },
    // 水印创建成功回调
    onSuccess: () => {},
    // 水印销毁前回调
    onBeforeDestroy: () => {},
    // 水印销毁后回调
    onDestroyed: () => {},
    // 监听错误回调
    onObserveError: (error: any) => { console.error('Watermark observe error:', error) },
  }

  // 销毁当前水印实例
  const destroy = (): void => {
    if (watermarkInstance.value) {
      watermarkInstance.value.destroy()
      watermarkInstance.value = null
    }
  }

  // 创建水印
  const create = (options: Partial<WatermarkOptions> = {}): void => {
    destroy() // 先销毁旧实例

    // 合并默认选项和传入的覆盖选项
    const finalOptions: Partial<WatermarkOptions> = { ...defaultOptions, ...options }

    // 创建新实例
    watermarkInstance.value = new Watermark(finalOptions)
    watermarkInstance.value.create()
  }

  // 组件卸载时自动销毁水印
  onBeforeUnmount(destroy)

  return {
    create,
    destroy,
  }
}
