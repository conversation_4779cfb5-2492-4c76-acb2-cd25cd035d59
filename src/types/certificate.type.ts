/**
 * 证书类型（为后续扩展做准备，暂时只使用了 '1' 和 '2'）
 */
export enum CertType {
  // 小学毕业证
  EDUCATION_PRIMARY = '1',
  // 初中毕业证
  EDUCATION_MIDDLE = '2',
}

/**
 * 证书数据结构
 */
export interface CertData {
  // 证书标题
  title: string
  // 证书类型
  type: CertType
  // 姓名
  xm: string
  // 性别
  xb: string
  // 出生日期
  csrq: string
  // 籍贯
  jg: string
  // 注册学号
  zcxh: string
  // 发证编号
  fzbh: string
  // 证书学历
  zsxl: string
  // 毕业时间
  bysj: string
  // 学历证书查询链接 (URL)
  xlUrl: string | null
  // 签发时间
  qfsj: string | null
  // 签发单位
  qfdw: string
  // 头像文件 (PATH)
  txFile: string | null
  // PDF 文件内容 (PATH)
  pdfFile?: string | null
  // 图片文件内容 (PATH)
  imageFile: string | null
  // OFD 文件内容 (PATH)
  ofdFile: string | null
  // 印章信息列表
  sealInfoList?: SealInfo[]
}

/**
 * 印章信息
 */
export interface SealInfo {
  // 印章名称
  sealName: string
  // 印章图片 (PATH)
  sealPic: string | null
  // 签章时间
  sealTime: string
}
