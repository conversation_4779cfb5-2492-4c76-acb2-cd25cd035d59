潍坊高中段综合服务平台 H5 系统设计说明书

1. 项目概述
   1.1. 项目目标
   本项目旨在为潍坊市 2025 年高中阶段学校招生考试（中考）设计并开发一个官方手机端 Web 系统（H5）。核心目标是为考生及家长提供一个稳定、高效、清晰、安全的在线报名、信息填报、志愿选择、成绩与录取结果查询的一站式服务平台。
   1.2. 目标用户
   潍坊市应届初中毕业生（考生）
   考生家长
   1.3. 核心原则
   稳定性与安全性: 系统必须能承载高峰期并发访问，保障考生信息与志愿数据的安全。
   高效性与易用性: 交互流程必须简洁明了，符合移动端操作习惯，最大程度减少用户误操作。
   清晰性与时效性: 各阶段功能入口、开放时间、录取结果等信息需清晰展示，并严格按时间节点开放。
2. 技术栈规划
   本项目选用现代化的前端技术栈，以确保开发效率、项目可维护性及用户体验。
   前端框架：Vue 3
   原因：基于其优秀的性能、Composition API 带来的更灵活的代码组织方式和强大的社区生态。
   状态管理：Pinia
   原因：作为 Vue 官方推荐的状态管理库，它拥有极其简洁的 API、完整的 TypeScript 支持和模块化的设计，非常适合管理本应用中的用户、学生及配置状态。
   UI 组件库：Vant + TailwindCSS
   原因：Vant 是一套轻量、可靠的移动端组件库，能快速构建出符合移动端操作习惯的基础界面。TailwindCSS 作为一个功能类优先的 CSS 框架，可以极大地提升自定义样式和微调布局的效率，二者结合，兼顾了开发速度与定制自由度。
   构建工具：Vite
   原因：提供毫秒级的热模块更新（HMR）和极速的冷启动，显著提升开发体验。其基于 ESM 的构建方式也带来了更优的性能。
   包管理工具：pnpm
   原因：通过其独特的硬链接和符号链接机制，可以大幅节省磁盘空间并提升依赖安装速度。
3. 页面、路由与组件设计
   3.1. 核心用户流程
   graph TD
   A[用户进入系统] --> B{是否已绑定学生信息?};
   B -- 否 --> C[学生信息绑定页];
   C -- 绑定成功 --> D[首页/学生信息主页];
   B -- 是 --> D;
   D --> E[完善个人信息页];
   D --> F[志愿填报页];
   D --> G[录取结果查询页];
   D --> H[中考分数查询页];

3.2. 路由规划 (Router Plan)

路由路径 (Path)
页面组件 (Component)
页面名称 (Name)
描述
/login
LoginView.vue
Login
学生信息绑定页。未绑定用户默认重定向至此。
/
HomeView.vue
Home
首页/学生信息主页。展示学生卡片和各阶段志愿填报入口。
/profile
ProfileView.vue
Profile
考生信息完善页。用于填写联系方式、监护人等信息。
/volunteer/:type
VolunteerView.vue
Volunteer
动态志愿填报页。通过 :type 参数区分不同阶段的志愿填报。
/result/:type
ResultView.vue
Result
动态录取结果页。通过 :type 参数查询不同阶段的录取结果。
/score
ScoreView.vue
Score
中考分数查询页。

- (Not Found)
  NotFoundView.vue
  NotFound
  404 页面。

动态路由参数 :type 说明:
为了提高代码复用性，志愿填报和结果查询将使用动态路由。:type 参数的值将用于：
从配置中获取对应的标题、开放时间、志愿表单结构。
请求对应阶段的模拟数据。
:type 参数值
对应阶段
main
“3+4”高职、普高志愿
vocational
职教高考班、五年制高职、中专、技校志愿
private-supplementary
民办普高二次补录
vocational-supplementary
职教高考班和五年制高职二次补录

3.3. 组件化设计 (Componentization)
将通用 UI 元素抽象为可复用组件是提升开发效率和维护性的关键。
组件名 (Component Name)
描述
复用页面
StudentInfoCard.vue
学生基本信息卡片，包含照片、姓名、身份证/学籍号。可包含操作按钮插槽。
HomeView, ResultView, ScoreView
VolunteerStageCard.vue
首页上的志愿阶段卡片。接收阶段信息（标题、时间）作为 props，内部处理按钮（填报/查询）的禁用和跳转逻辑。
HomeView
VolunteerChoiceForm.vue
单个志愿填报表单项。包含学校、校区、专业等下拉选择框和重置按钮。通过 props 动态生成，例如是否需要“校区”、“专业”字段。
VolunteerView
PageLayout.vue
基础页面布局，包含统一的页头（标题栏）、返回按钮和内容区域。
所有页面
CountdownTimer.vue
时间控制器。用于显示“距开始/结束”的倒计时，或提示“已开始/已结束”。
VolunteerStageCard
FullScreenLoading.vue
全局加载动画，用于数据请求时提供反馈。
全局，通过 Pinia 状态控制
EmptyState.vue
空状态提示组件。例如，当录取结果未出或没有数据时显示。
ResultView, ScoreView

4. 数据结构与模拟 (Data & Mocking)
   为实现前后端分离开发和高效 Demo 演示，所有数据交互均通过请求本地 JSON 文件模拟。
   4.1. Mock 文件夹结构
   /mock
   ├── student.json # 当前已绑定的学生详细信息
   ├── config.json # 系统时间配置、阶段名称等
   ├── volunteerOptions.json # 所有志愿填报的可选项（学校、校区、专业列表）
   ├── myVolunteerData.json # 用户已保存的各阶段志愿填报数据
   └── admissionResults.json # 各阶段的录取结果

4.2. 核心数据模型 (JSON Schema)
/mock/student.json
{
"isBound": true,
"studentId": "G37070220100101XXXX",
"idCard": "37070220100101XXXX",
"name": "张三",
"photoUrl": "https://placehold.co/120x160/E2E8F0/4A5568?text=照片",
"gender": "男",
"middleSchool": "潍坊市实验中学",
"grade": "九年级",
"ethnicity": "汉族",
"contactPhone": "13812345678",
"postalCode": "261000",
"address": "山东省潍坊市奎文区幸福家园",
"guardian": {
"name": "张伟",
"relation": "父亲",
"phone": "13987654321"
},
"examSubjects": ["数学", "英语", "物理", "化学", "道德与法治", "历史"]
}

/mock/config.json
{
"scoreQueryStart": "2025-07-15 09:00:00",
"stages": {
"main": {
"title": "“3+4”高职、普高志愿",
"applicationStart": "2025-05-11 08:00:00",
"applicationEnd": "2025-05-15 20:00:00",
"resultStart": "2025-07-20 09:00:00",
"resultEnd": "2025-09-01 00:00:00"
},
"vocational": {
"title": "职教高考班、五年制高职、中专技校志愿",
"applicationStart": "2025-06-26 08:00:00",
"applicationEnd": "2025-06-30 20:00:00",
"resultStart": "2025-07-27 09:00:00",
"resultEnd": "2025-09-01 00:00:00"
},
"private-supplementary": {
"title": "民办普高二次补录",
"applicationStart": "2025-07-17 08:00:00",
"applicationEnd": "2025-07-18 20:00:00",
"resultStart": "2025-07-20 09:00:00",
"resultEnd": "2025-09-01 00:00:00"
},
"vocational-supplementary": {
"title": "职教高考班和五年制高职二次补录",
"applicationStart": "2025-07-31 08:00:00",
"applicationEnd": "2025-09-01 00:00:00",
"resultStart": "2025-08-03 09:00:00",
"resultEnd": "2025-09-01 00:00:00"
}
}
}

/mock/volunteerOptions.json (示例)
{
"schools": [
{ "id": "sch001", "name": "潍坊第一中学", "type": "public_high", "campuses": ["主校区", "滨海校区"] },
{ "id": "sch002", "name": "潍坊商业学校", "type": "3+4", "campuses": [] },
{ "id": "sch003", "name": "潍坊市育才学校", "type": "private_high", "campuses": [] }
],
"majors": [
{ "id": "maj001", "name": "计算机应用", "applicableSchoolIds": ["sch002"] },
{ "id": "maj002", "name": "学前教育", "applicableSchoolIds": ["sch002"] }
]
}

4.3. 状态管理 (Pinia) 规划
建议设立以下几个 Store 来管理应用状态：
userStore:
state: studentInfo, isLoggedIn (根据 studentInfo 是否有数据判断)
actions: bindStudent(), fetchStudentInfo(), updateStudentProfile()
configStore:
state: systemConfig (包含所有时间节点和阶段信息)
actions: fetchConfig() (应用启动时调用)
getters: getStageInfoByType(type), isApplicationOpen(type), isResultOpen(type)
volunteerStore:
state: volunteerOptions (学校、专业列表), myChoices
actions: fetchVolunteerOptions(), fetchMyChoices(type), saveMyChoices(type, data)
resultStore:
state: admissionResults
actions: fetchAdmissionResult(type) 5. 页面详细设计与交互
5.1. 登录/绑定页 (/login)
布局: 页面居中一个表单。
表单项:
输入框：身份证号或学籍号
输入框：考生姓名
输入框：学籍号后六位 (作为二次验证)
交互: 点击“绑定”按钮，调用 userStore.bindStudent()。成功后，路由跳转至 /。失败时，使用 Vant 的 Toast 组件提示错误信息。
5.2. 首页 (/)
布局:
顶部: StudentInfoCard 组件，展示基本信息，并包含“完善信息”和“分数查询”两个按钮。
主体: 垂直列表，循环渲染 VolunteerStageCard 组件，展示所有志愿填报阶段。
交互:
点击“完善信息” -> 跳转 /profile。
点击“分数查询” -> 跳转 /score (需判断是否到了开放时间)。
各阶段卡片上的按钮会根据 configStore 的时间判断其状态（禁用/激活）和功能（跳转填报/查询）。
5.3. 完善信息页 (/profile)
布局: 一个长的表单页面，使用 Vant 的 Form 和 Field 组件。
字段:
只读字段: 照片、姓名、性别、身份证号、学籍号、初中毕业学校、年级、民族。
关于“民族”：建议此字段也设为只读。中考报名信息极其严肃，通常以学籍库为准，不应允许用户随意修改，以防信息不一致带来的问题。
可填字段: 联系电话、邮政编码、联系地址、监护人姓名、与考生关系、联系电话。
多选框: 报考科目 (使用 Vant 的 CheckboxGroup)。
交互: 底部有“提交”按钮，点击后调用 userStore.updateStudentProfile()，成功后 Toast 提示并返回首页。
5.4. 志愿填报页 (/volunteer/:type)
布局:
顶部: 页面标题，根据 :type 动态显示，如““3+4”高职、普高志愿填报”。
主体: 根据 :type 从配置中读取该阶段需要填报的志愿列表，并循环渲染 VolunteerChoiceForm 组件。
动态逻辑:
VolunteerView.vue 在 onMounted 时获取路由参数 :type。
根据 :type 从 configStore 获取志愿结构（如：3个“3+4”志愿，4个普高志愿）。
每个 VolunteerChoiceForm 组件根据传入的 props 决定是否显示“校区”、“专业”选择器。
交互:
每个志愿项都有独立的“重置”按钮。
页面底部有“保存志愿”和“清空所有”按钮。
点击“保存志愿”，收集所有表单数据，调用 volunteerStore.saveMyChoices()。
5.5. 录取结果页 (/result/:type)
布局:
顶部: StudentInfoCard 组件。
主体:
如果 resultStore 中有对应 :type 的录取数据，则显示一个结果卡片（录取学校、校区、专业、录取状态）。
如果没有数据，则显示 EmptyState 组件，提示“录取结果尚未公布”或“您未参与本阶段录取”。
交互: 页面加载时调用 resultStore.fetchAdmissionResult(type) 获取数据。6. 总结与建议
这份设计文档提供了一个清晰、可执行的开发蓝图。通过路由驱动的动态页面和高度组件化的策略，可以最大限度地减少重复代码，提高开发效率和项目的可维护性。
下一步行动建议:
搭建项目骨架: 使用 pnpm create vite 初始化项目，并安装 Vue Router, Pinia, Vant, TailwindCSS 等依赖。
创建目录结构: 按照规划建立 /views, /components, /router, /stores, /mock 等文件夹。
实现 Pinia Stores: 优先编写好各个 Store 的 state 和 actions，并完成对 mock 文件的请求逻辑。
开发组件: 从基础组件（如 PageLayout, StudentInfoCard）开始，逐步开发业务组件。
组装页面: 最后，在 views 中引入组件，连接 Pinia，完成页面逻辑和路由跳转。



之前的页面views可以全部忽略，原有的 router 可以都删除，从头来过。登录页面暂时不需要实现。先设计页面和路由，内容可以不用实现。然后从学生页面开始实现，第一步学生基本信息的card，这个作为组件很多页面都需要。
注意查看 eslint 规则，