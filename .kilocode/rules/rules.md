# Kilo Code 模式开发规则 (适用于 cert-verify-app 项目)

## 1. 核心原则

- **语言:** 变量命名（使用英文）；代码注释、日志信息（使用中文）。与用户交流也必须使用中文。
- **环境:** 开发环境为 macOS，使用 pnpm 作为包管理器。所有相关命令（安装、运行、构建、Lint）需使用 pnpm。
- **聚焦任务:** 严格按照当前分配的任务执行，避免引入范围之外的任何代码修改或重构。
- **保留用户调整:** 如果用户对你生成的代码进行了调整，必须在后续操作中保留这些调整，不得覆盖。
- **代码风格:** 严格遵循以下代码风格规范，确保代码一致性和可读性。

## 2. TypeScript 规范

- **类型定义:**
  - 优先使用 `interface` 定义对象结构和类型别名。
  - 如果自定义类型超过一个文件引用，**必须** 统一存放在 [`src/types/`](src/types/) 目录下使用 `export` 导出。
  - 该目录[`src/types/`](src/types/) 下的类型已配置为全局自动导入，无需手动引入。
- **JSDoc:**
  - 保持注释格式的统一，内容应简洁明了。TypeScript 的类型系统已足够表达大部分类型信息。
  - JSDoc 仅可用于提供复杂的用法示例、重要的警告信息或对函数/变量目的的详细文字描述。可以使用必要的 `@param` 和`@deprecated` 标记。

## 3. Vue 3 规范

- **脚本语法:** **必须** 使用 `<script setup lang="ts">` 语法糖。
- **API 风格:** **必须** 使用 Composition API。禁止使用 Options API。
- **响应式:** 优先使用 `ref` 和 `reactive`。理解 `shallowRef`, `readonly` 等适用场景。

## 4. 样式规范 (UnoCSS/TailwindCSS)

- **类名:** **必须** 直接在模板的 `class` 属性中使用原子类名。
- **禁止:** **严禁** 在 `<style>` 块中使用 `@apply` 或任何 CSS 预处理指令（如 Sass/Less 嵌套、变量等）。所有样式通过原子类实现。

## 5. 自动导入

- 以下模块已配置自动导入，**禁止** 手动 `import`：
  - 框架核心: `vue`, `pinia`, `vue-router`, `@vueuse/core`, `@vueuse/head` 等。
  - 项目模块: [`src/composables/`](src/composables/), [`src/api/`](src/api/), [`src/types/`](src/types/) 下的所有导出。
- **验证:** 如不确定是否自动导入，可参考项目根目录下的 `auto-imports.d.ts` 和 `components.d.ts` 文件。
- **必要引入:** 如果某些库或模块未被自动导入（例如第三方库），则必须手动引入，且不要添加不必要的注释。

## 6. 代码质量与调试

- **Linting:** 在提交代码或遇到 ESLint 错误时，**必须** 首先尝试运行 `pnpm run lint` 命令自动修复格式问题。
- **错误处理:** 仔细修复 Lint 命令无法自动解决的其他 ESLint 报错或 TypeScript 类型错误。
- **调试:** 提供清晰的错误分析和解决方案。

## 7. 文件与项目结构

- 遵循现有的项目结构约定（如 [`src/types`](src/types/), [`src/composables`](src/composables/), [`src/views`](src/views/) 等）。
- 创建新文件时，放置在合适的目录下。

## 8. 文档 (README.md)

- 如果进行了重要的功能添加或修改，应考虑更新项目根目录下的 [`README.md`](README.md) 文件，说明新功能、用法或配置变化。

## 9. API 请求

- **文件位置:** 与后端接口交互的 API 请求函数应定义在 [`src/api`](src/api/) 目录下。
- **模块划分:** API 文件应根据后端服务的模块或资源进行划分。

## 10. 图标规范 (Iconify)

- **集成方式:** 项目通过 UnoCSS 的 `presetIcons` 功能集成了 [Iconify](https://iconify.design/) 图标库。图标以 CSS 类名的方式直接在模板中使用。
- **使用语法:** 图标的 CSS 类名遵循 `i-{collection}:{icon-name}` 的格式。其中 `{collection}` 是图标集的名称（前缀），`{icon-name}` 是具体的图标名称。可以通过添加额外的 TailwindCSS/UnoCSS 原子类来控制图标的大小、颜色等样式。
- **使用示例:**

  ```html
  <!-- 使用 MingCute 图标集中的 user 图标，并设置文本大小为 xl -->
  <div class="i-mingcute:user text-xl"></div>
  ```

- **可用图标集:** 当前项目已安装并推荐使用的图标集包括：
  - `mingcute`: MingCute Icon ([@iconify-json/mingcute](https://iconify.design/icon-sets/mingcute/))
- **使用原则:**
  - 应优先从上述已安装的图标集中选择合适的图标。
  - 避免引入额外的图标库或直接使用 SVG 文件，除非当前图标集无法满足需求且有充分理由。如有必要引入新图标集，需更新项目配置和本文档。
