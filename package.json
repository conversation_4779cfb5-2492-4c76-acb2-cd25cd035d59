{"name": "wf-edu-app", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.14.0", "scripts": {"dev": "vite", "publish:all": "pnpm run publish:staging && pnpm run publish:production", "publish:staging": "git stash && git checkout main && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:production": "git stash && git checkout release && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "postinstall": "npx simple-git-hooks", "lint": "autocorrect --fix && eslint . --fix"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "axios": "^1.11.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "unstorage": "^1.16.1", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1", "watermark-js-plus": "^1.6.3"}, "devDependencies": {"@antfu/eslint-config": "^4.19.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@iconify-json/mingcute": "^1.2.3", "@types/file-saver": "^2.0.7", "@types/node": "^24.1.0", "@unocss/eslint-config": "^66.3.3", "@unocss/eslint-plugin": "^66.3.3", "@unocss/reset": "^66.3.3", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.4", "autocorrect-node": "^2.14.0", "eslint": "^9.32.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-vue": "~10.4.0", "lint-staged": "^16.1.2", "simple-git-hooks": "^2.13.1", "typescript": "^5.9.2", "unocss": "66.2.3", "unocss-preset-scrollbar-hide": "^1.0.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "pnpm commitlint --edit ${1}"}, "lint-staged": {"*": ["autocorrect --fix", "eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "volta": {"node": "22.16.0"}}