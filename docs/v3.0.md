好的，收到最终的技术实现要求。这将是项目最终的、最完整的系统设计文档，整合了包括业务逻辑、页面路由、UI 设计、技术选型和 Demo 阶段数据策略在内的所有信息。

现在开始整合设计。

---

### **潍坊高中段综合服务平台 - 手机端 Web 系统设计文档 (最终版)**

**版本：** 3.0
**日期：** 2025-07-31

#### **1. 项目概述**

本项目旨在设计并开发“潍坊高中段综合服务平台”的手机端 Web 系统。该平台是为潍坊市 2025 年高中阶段学校招生考试（中考）指定的官方报名系统。系统核心目标是为考生及家长提供一个稳定、高效、清晰、安全的在线报名、信息填报、志愿选择、成绩与录取查询的一站式服务入口。

- **平台名称：** 潍坊高中段综合服务平台
- **核心作用：** 2025 年潍坊市高中段招生考试报名平台
- **目标用户：** 潍坊市中考考生及其监护人（家长可通过一个账户管理多名子女的报名事宜）

#### **2. 技术栈规划 (Technology Stack Plan)**

为确保开发效率、项目可维护性及用户体验，本项目选用现代化的前端技术栈。

- **前端框架：Vue3**
  - 原因：基于其优秀的性能、Composition API 带来的更灵活的代码组织方式和强大的社区生态。
- **状态管理：Pinia**
  - 原因：作为 Vue 官方推荐的状态管理库，它拥有极其简洁的 API、完整的 TypeScript 支持和模块化的设计，非常适合管理本应用中的用户、学生及配置状态。
- **UI 组件库：Vant + TailwindCSS**
  - 原因：**Vant** 是一套轻量、可靠的移动端组件库，能快速构建出符合移动端操作习惯的基础界面。**TailwindCSS** 作为一个功能类优先的 CSS 框架，可以极大地提升自定义样式和微调布局的效率，二者结合，兼顾了开发速度与定制自由度。
- **构建工具：Vite**
  - 原因：提供毫秒级的热模块更新（HMR）和极速的冷启动，显著提升开发体验。其基于 ESM 的构建方式也带来了更优的性能。
- **包管理工具：pnpm**
  - 原因：通过其独特的硬链接和符号链接机制，可以大幅节省磁盘空间并提升依赖安装速度。

#### **3. 页面规划 (Page Planning)**

系统采用**底部标签栏 (Tab Bar)** 作为全局导航，结合**页面栈**进行路由，提供清晰、沉浸式的用户体验。

##### **3.1 全局导航：底部标签栏 (Bottom Tab Bar)**

标签栏提供对应用三大核心模块的快速访问。

1.  **首页 (Home):** 核心功能入口，展示当前学生的报名时间轴。
2.  **公告 (Announcements):** 查阅最新的政策和通知。
3.  **我的 (My):** 管理家长账户和绑定的学生信息。

##### **3.2 页面视图详述**

| 所属模块                                                          | 页面/视图 (View)  | 核心功能与组件                                                                                                                                                    |
| :---------------------------------------------------------------- | :---------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **独立页面 (无 Tab Bar)**                                         |
| `AuthHome`                                                        | 登录/注册页       | 手机号输入框、密码/验证码输入框、登录/注册按钮、忘记密码。                                                                                                        |
| `Register`                                                        | 注册页            | 手机号、验证码、密码设置、用户协议。                                                                                                                              |
| `StudentSelection`                                                | 学生选择页        | 已绑定学生列表、添加新学生按钮。                                                                                                                                  |
| `AddStudent`                                                      | 添加学生页        | 考生姓名、身份证号、学籍号等信息输入。                                                                                                                            |
| **首页 (Home) 模块**                                              |
| `Dashboard`                                                       | 个人中心 (时间轴) | **顶部：** 当前学生姓名及切换入口。\<br\>**中部：** 以时间轴清晰展示“核心流程”和“可选流程”各节点，节点可点击跳转。\<br\>**底部：** 关键操作入口（如“立即报名”）。 |
| `Profile`, `VolunteerForms`, `QueryResult`, `AdmissionConfirm` 等 | 各业务流程页      | 从时间轴节点点击后，推入（Push）到导航栈中，覆盖 Tab Bar，完成操作后返回。                                                                                        |
| **公告 (Announcements) 模块**                                     |
| `AnnouncementsList`                                               | 公告列表页        | 列表展示《报名须知》、《中考政策》等所有文章。                                                                                                                    |
| `PolicyDetail`                                                    | 公告详情页        | 展示单篇公告的完整内容。                                                                                                                                          |
| **我的 (My) 模块**                                                |
| `MyProfile`                                                       | 个人中心          | 家长账户信息、修改密码入口、绑定的学生列表（可在此管理或添加）、退出登录按钮。                                                                                    |

---

#### **4. 路由规划 (Routing Planning)**

路由结构反映了 Tab Bar 的全局导航和参数化的学生 ID。

| 路径 (Path)                           | 对应页面          | 描述                             |
| :------------------------------------ | :---------------- | :------------------------------- |
| `/login`                              | AuthHome          | 登录页 (应用入口)                |
| `/register`                           | Register          | 注册页                           |
| `/select-student`                     | StudentSelection  | 学生选择页                       |
| **--- Tab Bar 根路径 ---**            |
| `/home`                               | Dashboard         | “首页”标签页，需携带学生 ID      |
| `/announcements`                      | AnnouncementsList | “公告”标签页                     |
| `/my`                                 | MyProfile         | “我的”标签页                     |
| **--- 学生相关业务路径 ---**          |
| `/student/:studentId/home`            | Dashboard         | 选定学生后的首页（时间轴）       |
| `/student/:studentId/profile`         | Profile           | 从首页跳转到学生信息页           |
| `/student/:studentId/volunteer/:type` | VolunteerForms    | 志愿填报页，type 区分普高/职教等 |
| `/student/:studentId/query/:type`     | QueryResult       | 结果查询页，type 区分成绩/录取   |
| `/announcements/:id`                  | PolicyDetail      | 从公告列表跳转到详情             |

---

#### **5. 样式规划 (Style Planning)**

在原有**权威、简洁、清晰**的原则基础上，重点规划 Tab Bar 和时间轴的视觉呈现。

- **Tab Bar 视觉：**
  - 采用 Vant 的 `van-tabbar` 组件。图标使用线性图标，清晰表意。
  - 当前激活的标签页，其图标和文字应高亮（使用蓝色主色调），与未激活的灰色状态形成对比。
- **时间轴设计：**
  - 使用 Vant 的 `van-steps` 组件作为基础或自行实现。
  - 节点状态通过颜色和图标明确区分：**灰色** (未开始)、**蓝色** (进行中)、**绿色 + 对勾** (已完成)、**橙色 + 感叹号** (待处理/待确认)。
  - 每个节点包含标题（如“普通高中报名”）和日期范围（“5.11-5.15”），并作为可点击区域。

---

#### **6. 开发与数据模拟 (Development & Data Mocking)**

为实现前后端分离开发和高效的 Demo 演示，数据层将进行模拟。

- **数据源：** 在项目根目录下创建 `/mock` 文件夹，用于存放所有模拟数据。数据以 `.json` 文件格式存储，禁止将数据硬编码在 Vue 组件中。
- **模拟文件结构示例：**
  - `/mock/config.json`: 存放所有可配置的时间节点。
    ```json
    {
      "mainRegistration": { "start": "2025-05-11 08:00:00", "end": "2025-05-15 20:00:00" },
      "scoreQuery": { "start": "2025-06-27 00:00:00" },
      "...": "..."
    }
    ```
  - `/mock/users.json`: 存放家长账户及绑定的学生 ID。
    ```json
    [
      { "phone": "13800138000", "password": "...", "linkedStudents": ["student_001", "student_002"] }
    ]
    ```
  - `/mock/students.json`: 存放所有学生的详细信息。
    ```json
    [
      { "id": "student_001", "name": "李华", "idCard": "...", "profile": {...}, "status": {...} },
      { "id": "student_002", "name": "张伟", "idCard": "...", "profile": {...}, "status": {...} }
    ]
    ```
  - `/mock/announcements.json`: 存放公告列表。
- **数据请求：** 在 Vue 应用中，通过 `fetch` API 或 `axios` 请求这些本地 JSON 文件，模拟 API 交互。例如，在 Pinia 的 store 中封装 action，用于异步获取和处理这些模拟数据。

---

#### **7. 业务功能规划 (Business Function Planning)**

- **后台可配置节点：**
  - 系统的所有业务流程，特别是其**起始和结束时间**，都必须由后台动态配置，而非前端硬编码。前端应用在启动时，应首先加载 `/mock/config.json` 中的时间配置，并以此作为控制所有功能入口（如按钮的禁用/启用，页面的开放/锁定）的唯一依据。
- **其余业务逻辑：**
  - **用户认证**、**多学生管理**、**信息填报**、**志愿选择**、**查询确认**等核心逻辑均与 `v2.0` 文档保持一致，但其实现将严格遵循上述技术栈和数据模拟策略。所有业务操作都必须与 Pinia store 进行交互，以实现状态的响应式更新和全局共享。
