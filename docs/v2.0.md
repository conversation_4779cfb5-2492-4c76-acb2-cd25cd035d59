好的，已收到您的修改指令。我们将根据您的新要求，对系统设计文档进行全面修订。

核心修改点归纳如下：

1.  **登录方式变更：** 移除微信扫码，采用手机号 + 密码/验证码的注册和登录体系。
2.  **账户结构变更：** 支持“一户多生”，即一个家长的手机号账户下可绑定和管理多名学生。
3.  **界面设计优化：** 在个人中心采用“时间轴”设计，清晰展示核心与可选的报名流程。
4.  **模块功能增强：** 增加独立的“公告通知”模块。

现在开始重新设计。

---

### **潍坊高中段综合服务平台 - 手机端 Web 系统设计文档 (修订版)**

**版本：** 2.0
**日期：** 2025-07-31

#### **1. 项目概述**

本项目旨在设计并开发“潍坊高中段综合服务平台”的手机端 Web 系统。该平台是为潍坊市 2025 年高中阶段学校招生考试（中考）指定的官方报名系统。系统核心目标是为考生及家长提供一个稳定、高效、清晰、安全的在线报名、信息填报、志愿选择、成绩与录取查询的一站式服务入口。

- **平台名称：** 潍坊高中段综合服务平台
- **核心作用：** 2025 年潍坊市高中段招生考试报名平台
- **目标用户：** 潍坊市中考考生及其监护人（家长可通过一个账户管理多名子女的报名事宜）

---

#### **2. 页面规划 (Page Planning)**

系统页面将围绕家长“**注册/登录 -\> 选择学生 -\> 沿时间轴办理业务 -\> 查询结果**”的核心路径进行设计。

| 页面/视图 (View)        | 页面名称                  | 核心功能与组件                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 备注                                                                                  |
| :---------------------- | :------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------ |
| **登录/注册模块**       |
| `AuthHome`              | 登录/注册页               | 系统名称、Logo、手机号输入框、密码/验证码输入框、登录按钮、获取验证码按钮、忘记密码入口、“立即注册”入口。                                                                                                                                                                                                                                                                                                                                                                              | 系统的统一入口，界面简洁清晰。                                                        |
| `Register`              | 注册页                    | 手机号输入框、获取验证码按钮、设置密码、确认密码、注册协议勾选框、注册按钮。                                                                                                                                                                                                                                                                                                                                                                                                           | 用于创建家长账户。                                                                    |
| `StudentSelection`      | 学生选择页                | 已绑定学生列表（显示学生姓名、状态）、“添加新学生”按钮。                                                                                                                                                                                                                                                                                                                                                                                                                               | 家长登录后，若账户下有多于一名学生，则进入此页面进行选择。                            |
| `AddStudent`            | 添加学生页                | 考生姓名、身份证号、学籍号等信息输入框、提交绑定按钮。                                                                                                                                                                                                                                                                                                                                                                                                                                 | 家长在此页面将子女信息与其账户关联。                                                  |
| **核心功能模块**        |
| `Dashboard`             | **个人中心 (时间轴视图)** | **顶部：** 当前操作学生姓名显示区（如“当前学生：李华”），提供切换学生入口。\<br\>**中部核心区：** **时间轴设计**，垂直或水平展示报名全流程。分为“核心流程”和“可选流程”两大段。\<br\> \* **核心流程节点：** 报名 (5.11-15) -\> 成绩查询 (6.27) -\> 职教志愿 (6.26-30) -\> 录取查询 (7.13)。\<br\> \* **可选流程节点：** 跨区报名、民办补录、职教补录等。\<br\>每个节点清晰标注日期、当前状态（未开始/进行中/已完成），并作为功能入口直接点击跳转。\<br\>**底部：** “公告通知”模块入口。 | **设计的核心页面。** 时间轴为用户提供了清晰的“待办事项”和进度感知，极大提升用户体验。 |
| `Announcements`         | 公告通知列表页            | 以列表形式展示所有公告，如《报名须知》、《中考政策》等，点击可查看详情。                                                                                                                                                                                                                                                                                                                                                                                                               | 政策信息的集中地。                                                                    |
| `PolicyDetail`          | 公告详情页                | 展示单篇公告或政策的完整内容。                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                                                                       |
| `Profile`               | 考生基本信息              | 考生信息表单，包含“保存并提交”按钮。                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 所有操作均针对当前选定的学生。                                                        |
| `VolunteerHome`         | 志愿填报主页              | 分类展示不同批次的志愿入口，根据系统时间和学生状态自动开放或关闭。                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                                       |
| `VolunteerForms`        | 各类志愿填报页            | 包括“3+4”、普通高中、职教、补录等各类志愿的表单。                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                                                                       |
| **查询与确认模块**      |
| `QueryResult_Score`     | 成绩查询页                | 显示当前选定学生的各科成绩、总分。                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                                       |
| `QueryResult_Admission` | 录取查询页                | 清晰展示当前选定学生的录取状态、录取学校、录取专业。                                                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                       |
| `AdmissionConfirm`      | 录取确认页                | 显示投档信息，提供“同意录取”和“放弃录取”按钮及倒计时。                                                                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                       |

---

#### **3. 路由规划 (Routing Planning)**

路由设计将体现“**以学生为中心**”的账户模型，通过 URL 参数区分不同学生。

| 路径 (Path)                                  | 对应页面              | 描述                                  |
| :------------------------------------------- | :-------------------- | :------------------------------------ |
| `/`                                          | AuthHome              | 根路径，导向登录页。                  |
| `/login`                                     | AuthHome              | 登录页。                              |
| `/register`                                  | Register              | 家长账户注册页。                      |
| `/select-student`                            | StudentSelection      | 学生选择页。                          |
| `/add-student`                               | AddStudent            | 添加学生到当前账户。                  |
| `/announcements`                             | Announcements         | 公告通知列表页。                      |
| `/announcements/:id`                         | PolicyDetail          | 公告详情。                            |
| **--- 以下为学生相关路由，需先选择学生 ---** |
| `/student/:studentId/dashboard`              | Dashboard             | **核心：** 特定学生的个人中心时间轴。 |
| `/student/:studentId/profile`                | Profile               | 学生的个人信息。                      |
| `/student/:studentId/volunteer`              | VolunteerHome         | 学生的志愿填报主页。                  |
| `/student/:studentId/volunteer/regular`      | VolunteerForms        | 学生填报普通高中志愿。                |
| `/student/:studentId/query/score`            | QueryResult_Score     | 学生的成绩查询。                      |
| `/student/:studentId/query/admission`        | QueryResult_Admission | 学生的录取结果查询。                  |
| `/student/:studentId/confirm/admission`      | AdmissionConfirm      | 学生的录取确认。                      |
| `/logout`                                    | -                     | 登出操作，重定向到 `/`。              |

---

#### **4. 样式规划 (Style Planning)**

在原有的**权威、简洁、清晰**的原则基础上，重点突出时间轴设计的直观性。

- **色彩与字体：** 保持不变（蓝色主色调，系统无衬线字体）。
- **布局与组件：**
  - **时间轴设计：**
    - 采用垂直或水平布局，节点清晰，连接线贯穿。
    - **状态可视化：** 使用不同颜色或图标区分节点状态：灰色（未开始）、蓝色/高亮（进行中）、绿色/打勾（已完成）、橙色（待处理）。
    - **层级区分：** 在视觉上将“核心流程”与“可选流程”区分开，如放置在不同区域或用不同样式的节点表示。
  - **多学生管理：** 在页面顶部或侧边栏始终显示当前操作的学生姓名，并提供一个易于发现的“切换学生”下拉菜单或按钮。
  - **公告模块：** 在个人中心（Dashboard）使用卡片式设计，醒目地展示最新的 2-3 条公告标题，并有“查看更多”的链接。

---

#### **5. 业务功能规划 (Business Function Planning)**

业务逻辑的核心变更为**家长账户下的多学生管理模式**。

- **5.1 用户认证与授权 (核心变更)**

  - **家长账户体系：** 系统以家长的手机号作为唯一账户 `(AccountID)`。用户通过手机号 + 密码/验证码进行注册和登录。
  - **学生绑定与管理：**
    - 家长登录后，可以通过输入子女的“姓名 + 身份证号 + 学籍号”进行验证，验证通过后，该学生 `(StudentID)` 即与家长的 `AccountID` 绑定。
    - 一个 `AccountID` 可绑定多个 `StudentID`。
  - **操作流程：** 家长登录后，系统检查其绑定的学生数量。若大于 1，则强制跳转到 `select-student` 页面。选择一个学生后，后续所有操作（查看信息、填报志愿、查询结果）都将携带该 `StudentID` 进行，数据互相隔离。

- **5.2 个人中心与流程引导 (功能优化)**

  - **时间轴驱动：** 个人中心以时间轴为核心，取代简单的功能按钮列表。用户通过点击时间轴上的节点，直接进入对应的功能页面（如点击“报名”节点进入信息填报页）。
  - **状态同步：** 学生完成某项操作后（如提交志愿），对应时间轴节点的状态应立即更新为“已完成”。

- **5.3 公告通知模块 (新增)**

  - 后台提供公告管理功能，可发布、编辑、置顶《报名须知》、《中考政策》等文章。
  - 前端在个人中心醒目位置展示最新公告，并提供完整的公告列表页供用户查阅。

- **5.4 其余业务功能**

  - **信息填报、志愿管理、查询服务、录取确认等**功能保持不变，但所有操作的**前提**都是已经选定了具体要操作的学生。数据库层面，所有与报名流程相关的数据表都必须包含 `StudentID` 字段，以确保数据归属的正确性。
