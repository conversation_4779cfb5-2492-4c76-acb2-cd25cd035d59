{"compilerOptions": {"target": "esnext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"], "@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client"], "allowJs": false, "strict": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "vueCompilerOptions": {"target": 3}, "include": ["*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["dist", "node_modules"]}